package com.knet.order.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 绑定Confirm和Return回调
 **/

@Configuration
public class RabbitConfig {

    @Bean
    public TopicExchange orderExchange() {
        // 订单业务交换机
        return new TopicExchange("order-exchange", true, false);
    }

    @Bean
    public Queue orderQueue() {
        // 订单队列（带死信配置）
        return QueueBuilder
                .durable("order-queue." + "order-services")
                .withArgument("x-dead-letter-exchange", "DLX")
                .withArgument("x-dead-letter-routing-key", "order.*")
                .build();
    }

    @Bean
    public Binding orderBinding() {
        // 队列绑定
        return BindingBuilder
                .bind(orderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    @Bean
    public DirectExchange dlxExchange() {
        // 死信交换机（自动创建）
        return new DirectExchange("DLX", true, false);
    }

    @Bean
    public Queue dlxQueue() {
        // 死信队列
        return QueueBuilder
                .durable("dlx.order.queue")
                .build();
    }

    @Bean
    public Binding dlxBinding() {
        // 死信绑定
        return BindingBuilder
                .bind(dlxQueue())
                .to(dlxExchange())
                .with("order.*");
    }
}
