#!/bin/bash

# 安全检查脚本 - 验证Log4Shell防护配置

echo "=== Log4Shell 安全检查 ==="

# 检查主pom.xml中的log4j2版本
echo "1. 检查主pom.xml中的log4j2版本..."
if grep -q "log4j2.version.*2\.20\.0" pom.xml; then
    echo "✓ 主pom.xml已配置安全的log4j2版本 (2.20.0)"
else
    echo "✗ 主pom.xml未配置安全的log4j2版本"
fi

# 检查logback配置文件
echo ""
echo "2. 检查logback配置文件..."
modules=("user-services" "goods-services" "gateway" "order-services" "payment-services" "oauth-services")

for module in "${modules[@]}"; do
    logback_file="${module}/src/main/resources/logback-spring.xml"
    if [ -f "$logback_file" ]; then
        if grep -q "安全增强版本" "$logback_file" && grep -q "replace.*msg" "$logback_file"; then
            echo "✓ ${module}: logback配置已安全增强"
        else
            echo "✗ ${module}: logback配置需要安全增强"
        fi
    else
        echo "✗ ${module}: 缺少logback配置文件"
    fi
done

# 检查安全配置文件
echo ""
echo "3. 检查安全配置文件..."
if [ -f "common/src/main/resources/log4j2-security.properties" ]; then
    if grep -q "log4j2.formatMsgNoLookups=true" "common/src/main/resources/log4j2-security.properties"; then
        echo "✓ log4j2安全配置文件存在且配置正确"
    else
        echo "✗ log4j2安全配置文件配置不正确"
    fi
else
    echo "✗ 缺少log4j2安全配置文件"
fi

# 检查启动脚本
echo ""
echo "4. 检查安全启动脚本..."
if [ -f "security-startup.sh" ]; then
    if grep -q "log4j2.formatMsgNoLookups=true" "security-startup.sh"; then
        echo "✓ 安全启动脚本存在且配置正确"
    else
        echo "✗ 安全启动脚本配置不正确"
    fi
else
    echo "✗ 缺少安全启动脚本"
fi

# 检查构建脚本
echo ""
echo "5. 检查构建脚本安全配置..."
if [ -f "build.sh" ]; then
    if grep -q "log4j2.formatMsgNoLookups=true" "build.sh"; then
        echo "✓ 构建脚本已包含安全参数"
    else
        echo "✗ 构建脚本缺少安全参数"
    fi
else
    echo "✗ 缺少构建脚本"
fi

# 扫描可能的log4j依赖
echo ""
echo "6. 扫描可能的不安全log4j依赖..."
echo "正在检查所有pom.xml文件..."

unsafe_found=false
find . -name "pom.xml" -exec grep -l "log4j.*1\." {} \; 2>/dev/null | while read file; do
    echo "⚠️  发现可能不安全的log4j 1.x依赖: $file"
    unsafe_found=true
done

if [ "$unsafe_found" = false ]; then
    echo "✓ 未发现不安全的log4j 1.x依赖"
fi

echo ""
echo "=== 安全检查完成 ==="
echo ""
echo "建议的安全启动命令："
echo "export PROFILE=prd"
echo "export JAR_FILE=your-service.jar"
echo "source ./security-startup.sh"
echo "java \$ALL_OPTS -jar \$JAR_FILE"
