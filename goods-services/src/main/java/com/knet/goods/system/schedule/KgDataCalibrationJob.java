package com.knet.goods.system.schedule;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.enums.ProductStatus;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.third.req.KnetGroupGetInventoryReq;
import com.knet.goods.model.dto.third.resp.KnetInventoryDataVo;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.service.IThirdApiService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/19 11:11
 * @description: 校准来自kg的数据（同步库存）
 */
@Slf4j
@Component
public class KgDataCalibrationJob {
    @Resource
    private IThirdApiService thirdApiService;
    @Resource
    private KnetProductMapper knetProductMapper;

    /**
     * 同步库存数据任务
     * 支持的任务参数格式：
     * 1. 空参数：同步所有SKU的库存
     * 2. 单个SKU：直接传入SKU字符串，如 "AJ1-123"
     * - skus: 要同步的SKU列表，为空则同步所有
     *
     * @return 任务执行结果
     */
    @XxlJob("syncInventoryFromKgBySku")
    public ReturnT<String> syncInventoryFromKgBySku() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("任务 syncInventoryFromKgBySku 开始执行，任务参数: {}", jobParam);
        XxlJobHelper.log("开始执行库存同步任务，参数: {}", jobParam);
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("syncInventoryFromKgBySku");
        stopWatch.start();
        try {
            // 解析任务参数
            List<String> skus = new ArrayList<>();
            skus.add(jobParam);
            int updatedCount = syncInventory(skus);
            stopWatch.stop();
            String resultMsg = String.format("库存同步任务执行成功，共更新 %d 个商品，耗时: %d ms", updatedCount, stopWatch.getTotalTimeMillis());
            log.info(resultMsg);
            XxlJobHelper.log(resultMsg);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            String errorMsg = String.format("库存同步任务执行失败，耗时: %d ms, 错误: %s", stopWatch.getTotalTimeMillis(), e.getMessage());
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.handleFail(e.getMessage());
            return ReturnT.FAIL;
        }
    }

    /**
     * 执行库存同步
     *
     * @param skus 要同步的SKU列表，为空则同步所有
     * @return 更新的商品数量
     */
    private int syncInventory(List<String> skus) {
        int totalUpdated = 0;
        try {
            KnetGroupGetInventoryReq req = new KnetGroupGetInventoryReq();
            if (CollUtil.isNotEmpty(skus)) {
                req.setSkus(skus);
            }
            List<KnetInventoryDataVo> inventoryData = thirdApiService.getInventoryData(req);
            if (CollUtil.isEmpty(inventoryData)) {
                log.info("未获取到库存数据");
                XxlJobHelper.log("未获取到库存数据");
                // kg未获取数据，将req skus 商品全部更新为下架
                if (CollUtil.isNotEmpty(skus)) {
                    LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper
                            .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                            .in(KnetProduct::getSku, skus)
                            .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
                    return knetProductMapper.update(null, updateWrapper);
                }
                return 0;
            }
            log.info("获取到 {} 条库存数据", inventoryData.size());
            XxlJobHelper.log("获取到 {} 条库存数据", inventoryData.size());
            // 按批次处理库存数据，每批最多1000条
            List<List<KnetInventoryDataVo>> batches = CollUtil.split(inventoryData, 1000);
            for (List<KnetInventoryDataVo> batch : batches) {
                int batchUpdated = processBatch(batch);
                totalUpdated += batchUpdated;
                log.info("处理批次完成，更新 {} 条记录", batchUpdated);
                XxlJobHelper.log("处理批次完成，更新 {} 条记录", batchUpdated);
            }
            return totalUpdated;
        } catch (Exception e) {
            log.error("同步库存数据失败: {}", e.getMessage(), e);
            XxlJobHelper.log("同步库存数据失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 处理一批库存数据
     * 实现SQL逻辑：
     * UPDATE FROM knetb2b.`knet_product` k set k.status='OFF_SALE'
     * WHERE k.one_id NOT IN(
     * SELECT one_id FROM ocean_flow.`knet_product_listing`
     * WHERE knet_listing_status IN('CREATED', 'UPDATED') AND `sku` LIKE '1203A537 400'
     * ) AND k.sku='1203A537 400'
     *
     * @param batch 库存数据批次
     * @return 更新的记录数
     */
    private int processBatch(List<KnetInventoryDataVo> batch) {
        if (CollUtil.isEmpty(batch)) {
            return 0;
        }
        int updatedCount = 0;
        try {
            // 按SKU分组库存数据
            Map<String, List<KnetInventoryDataVo>> skuToInventoryMap = batch
                    .stream()
                    .filter(item -> item.getSku() != null && !item.getSku().isEmpty())
                    .collect(Collectors.groupingBy(KnetInventoryDataVo::getSku));
            if (skuToInventoryMap.isEmpty()) {
                log.warn("没有有效的SKU数据");
                XxlJobHelper.log("没有有效的SKU数据");
                return 0;
            }
            // 处理每个SKU的库存数据
            for (Map.Entry<String, List<KnetInventoryDataVo>> entry : skuToInventoryMap.entrySet()) {
                String sku = entry.getKey();
                List<KnetInventoryDataVo> inventoryItems = entry.getValue();
                List<String> validOneIds = inventoryItems.stream()
                        .map(KnetInventoryDataVo::getOneId)
                        .filter(oneId -> oneId != null && !oneId.isEmpty())
                        .distinct()
                        .collect(Collectors.toList());
                if (validOneIds.isEmpty()) {
                    log.warn("SKU: {} 没有有效的oneId数据", sku);
                    XxlJobHelper.log("SKU: {} 没有有效的oneId数据", sku);
                    continue;
                }
                // 1. 将不在validOneIds列表中的商品状态设置为OFF_SALE
                LambdaUpdateWrapper<KnetProduct> offSaleWrapper = new LambdaUpdateWrapper<>();
                offSaleWrapper
                        .eq(KnetProduct::getSku, sku)
                        .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                        .notIn(CollUtil.isNotEmpty(validOneIds), KnetProduct::getOneId, validOneIds)
                        .set(KnetProduct::getStatus, ProductStatus.OFF_SALE);
                int updatedRows = knetProductMapper.update(null, offSaleWrapper);
                if (updatedRows > 0) {
                    updatedCount += updatedRows;
                    String message = String.format("SKU: %s 下架了 %d 个商品", sku, updatedRows);
                    log.info(message);
                    XxlJobHelper.log(message);
                }
            }
            return updatedCount;
        } catch (Exception e) {
            log.error("处理库存批次失败: {}", e.getMessage(), e);
            XxlJobHelper.log("处理库存批次失败: {}", e.getMessage());
            throw e;
        }
    }
}
