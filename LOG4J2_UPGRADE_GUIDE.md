# Log4j2 安全升级指南

## 概述

本项目已完成从 log4j 1.x 到 log4j2 2.20.0 的安全升级，主要目的是修复 Log4Shell (CVE-2021-44228) 等高危安全漏洞。

## 升级内容

### 1. 依赖管理升级

#### 主 pom.xml 变更
- ✅ 移除了不安全的 `log4j.version=1.2.17`
- ✅ 添加了安全的 `log4j2.version=2.20.0`
- ✅ 添加了 log4j2-bom 依赖管理
- ✅ 强制使用安全版本的 log4j-core 和 log4j-api

#### Common 模块变更
- ✅ 添加了 log4j2 安全依赖
- ✅ 确保所有子模块使用统一的安全版本

### 2. 日志配置升级

#### Logback 配置增强 (所有模块)
- ✅ 添加了安全注释和配置说明
- ✅ 启用了 NopStatusListener 防止状态信息泄露
- ✅ 使用 `%replace(%msg){'[\r\n\t]', '_'}` 防止日志注入攻击
- ✅ 优化了滚动策略，支持大小和时间双重滚动
- ✅ 添加了异步 Appender 提升性能
- ✅ 错误日志独立存储，保留时间更长 (60天)
- ✅ 添加了防止日志注入的 Logger 配置

#### 涉及的模块
- user-services
- goods-services  
- gateway
- order-services
- payment-services
- oauth-services

### 3. 安全配置文件

#### log4j2-security.properties
位置: `common/src/main/resources/log4j2-security.properties`

关键安全配置：
```properties
log4j2.formatMsgNoLookups=true
log4j2.enableJndiLookup=false
log4j2.enableJndiJms=false
log4j2.enableJndiContextSelector=false
```

### 4. 安全启动脚本

#### security-startup.sh
- ✅ 包含完整的 Log4Shell 防护参数
- ✅ JVM 性能优化参数
- ✅ Spring Boot 安全配置
- ✅ 使用示例和说明

#### 关键安全参数
```bash
-Dlog4j2.formatMsgNoLookups=true
-Dlog4j2.enableJndiLookup=false
-Dlog4j2.enableJndiJms=false
-Dlog4j2.enableJndiContextSelector=false
-Dcom.sun.jndi.rmi.object.trustURLCodebase=false
-Dcom.sun.jndi.cosnaming.object.trustURLCodebase=false
```

### 5. 构建脚本升级

#### build.sh
- ✅ 添加了 Maven 构建时的安全参数
- ✅ 确保构建过程也受到保护

## 使用方法

### 1. 构建项目
```bash
./build.sh
```

### 2. 安全启动应用
```bash
# 设置环境和JAR文件
export PROFILE=prd
export JAR_FILE=user-services/target/user-services-1.0-SNAPSHOT.jar

# 加载安全参数
source ./security-startup.sh

# 启动应用
java $ALL_OPTS -jar $JAR_FILE
```

### 3. 安全检查
```bash
./security-check.sh
```

## 安全特性

### 1. Log4Shell 防护
- ✅ 禁用 JNDI 查找功能
- ✅ 禁用消息格式化中的查找
- ✅ 禁用所有不安全的查找机制

### 2. 日志注入防护
- ✅ 过滤换行符和制表符
- ✅ 限制日志消息格式
- ✅ 禁用危险的 Logger

### 3. 性能优化
- ✅ 异步日志写入
- ✅ 合理的缓冲区大小
- ✅ 智能的日志滚动策略

### 4. 运维友好
- ✅ 错误日志独立存储
- ✅ 可配置的保留策略
- ✅ 文件大小限制

## 验证方法

### 1. 运行安全检查
```bash
./security-check.sh
```

### 2. 检查日志输出
启动应用后检查日志目录：
```
logs/
├── user-services/
│   ├── info.log
│   └── error.log
├── goods-services/
│   ├── info.log
│   └── error.log
└── ...
```

### 3. 验证安全参数
检查应用启动日志中是否包含安全参数。

## 注意事项

1. **必须使用安全启动脚本**: 直接使用 `java -jar` 启动不会包含安全参数
2. **定期更新**: 建议定期检查 log4j2 的最新安全版本
3. **监控日志**: 注意监控错误日志中的异常情况
4. **备份配置**: 升级前请备份原有配置文件

## 相关文件清单

- `pom.xml` - 主依赖管理
- `common/pom.xml` - 公共模块依赖
- `*/src/main/resources/logback-spring.xml` - 各模块日志配置
- `common/src/main/resources/log4j2-security.properties` - 安全配置
- `security-startup.sh` - 安全启动脚本
- `security-check.sh` - 安全检查脚本
- `build.sh` - 安全构建脚本

## 技术支持

如有问题，请检查：
1. 运行 `./security-check.sh` 确认配置正确
2. 查看应用启动日志
3. 检查 `logs/*/error.log` 中的错误信息
