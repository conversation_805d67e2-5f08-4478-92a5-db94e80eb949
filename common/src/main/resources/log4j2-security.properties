# Log4j2 安全配置文件
# 防止 Log4Shell (CVE-2021-44228) 等安全漏洞

# 禁用 JNDI 查找功能，防止 Log4Shell 攻击
log4j2.formatMsgNoLookups=true

# 禁用不安全的 lookup 功能
log4j2.enableJndiLookup=false
log4j2.enableJndiJms=false
log4j2.enableJndiContextSelector=false

# 设置安全的配置
log4j2.trustStoreLocation=
log4j2.trustStorePassword=

# 禁用远程配置加载
log4j2.configurationFile=

# 设置日志级别限制
log4j2.level=INFO

# 安全模式配置
log4j2.isWebApp=false
log4j2.enableThreadlocals=false

# 防止栈溢出
log4j2.maxReusableMessageSize=518
log4j2.maxStringBuilderSize=4096

# 异步日志配置
log4j2.asyncLoggerConfigRingBufferSize=262144
log4j2.asyncLoggerConfigWaitStrategy=Block

# 禁用不安全的功能
log4j2.enableDirectEncoders=false
