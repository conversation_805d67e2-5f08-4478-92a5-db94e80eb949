package com.knet.payment.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.payment.model.dto.req.UserBalanceQueryRequest;
import com.knet.payment.model.dto.req.UserDeductRequest;
import com.knet.payment.model.dto.req.UserRechargeRequest;
import com.knet.payment.model.dto.req.UserWalletQueryRequest;
import com.knet.payment.model.dto.res.SysWalletRecordResp;
import com.knet.payment.model.dto.res.UserBalanceResponse;
import com.knet.payment.model.dto.res.UserDeductResponse;
import com.knet.payment.model.dto.res.UserRechargeResponse;
import com.knet.payment.service.ISysUserWalletService;
import com.knet.payment.service.ISysWalletRecordService;
import com.knet.payment.system.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:14
 * @description: 支付服务-用户钱包控制器
 */
@Slf4j
@RestController
@RequestMapping("/wallet")
@Tag(name = "支付服务-用户钱包控制器", description = "支付服务-用户钱包控制器")
public class WalletController {

    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISysWalletRecordService sysWalletRecordService;
    @Resource
    private ISysUserWalletService sysUserWalletService;

    /**
     * 查询用户钱包记录
     *
     * @param request 查询请求
     * @return 用户钱包记录列表
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(summary = "查询用户钱包记录", description = "查询用户钱包记录")
    @PostMapping("/list")
    public HttpResult<IPage<SysWalletRecordResp>> list(@RequestBody UserWalletQueryRequest request) {
        return HttpResult.ok(sysWalletRecordService.findWalletRecordList(request));
    }

    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(summary = "查询用户余额", description = "根据userId查询用户余额信息")
    @GetMapping("/balance")
    public HttpResult<UserBalanceResponse> getUserBalance() {
        log.info("查询用户余额请求 ");
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        UserBalanceResponse response = sysUserWalletService.getUserBalance(new UserBalanceQueryRequest(Long.valueOf(userId)));
        return HttpResult.ok(response);
    }

    /**
     * 用户充值
     *
     * @param request 充值请求
     * @return 充值响应
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(description = "用户钱包充值，保存充值操作记录")
    @PostMapping("/recharge")
    public HttpResult<UserRechargeResponse> recharge(@Validated @RequestBody UserRechargeRequest request) {
        log.info("用户充值请求: {}", request);
        UserRechargeResponse response = sysUserWalletService.recharge(request);
        return HttpResult.ok(response);
    }

    /**
     * 用户扣款
     *
     * @param request 用户扣款请求
     * @return 用户扣款响应
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(description = "用户钱包扣款，保存用户扣款操作记录")
    @PostMapping("/deduct")
    public HttpResult<UserDeductResponse> deduct(@Validated @RequestBody UserDeductRequest request) {
        log.info("用户扣款请求: {}", request);
        UserDeductResponse response = sysUserWalletService.deduct(request);
        return HttpResult.ok(response);
    }
}
