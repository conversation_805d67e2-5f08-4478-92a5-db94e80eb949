package com.knet.payment.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.RateLimiter;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;

import static com.knet.common.constants.SystemConstant.API_RATE_LIMIT;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:55
 * @description: 流量控制切面
 */
@Aspect
@Component
public class RateLimiterAspect {

    @Resource
    private RedisCacheUtil redisCacheUtil;
    /**
     * SpEL表达式解析器
     */
    private final ExpressionParser parser = new SpelExpressionParser();
    private final ParameterNameDiscoverer paramNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around("@annotation(rateLimiter)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimiter rateLimiter) throws Throwable {
        // 1. 生成动态限流Key（结合SpEL）
        String dynamicKey = parseKey(rateLimiter.keyExpression(), joinPoint);
        String fullKey = String.format(API_RATE_LIMIT, dynamicKey);
        // 2. 执行令牌桶算法
        boolean allowed = redisCacheUtil.isAllowed(fullKey, rateLimiter.capacity(), rateLimiter.refillRate());
        if (!allowed) {
            throw new ServiceException(rateLimiter.message());
        }
        return joinPoint.proceed();
    }

    /**
     * 解析SpEL表达式生成动态Key
     */
    private String parseKey(String expression, ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        if (StrUtil.isEmpty(expression)) {
            return signature.getMethod().getName() + "_" + Arrays.hashCode(args);
        }
        EvaluationContext context = new StandardEvaluationContext();
        // 获取方法参数名
        String[] paramNames = paramNameDiscoverer.getParameterNames(method);
        if (paramNames != null) {
            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }
        }
        return parser.parseExpression(expression).getValue(context, String.class);
    }
}
