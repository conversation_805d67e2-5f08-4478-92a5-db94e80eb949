package com.knet.payment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2025/3/12 14:15
 * @description: 退款记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_refund_record", description = "退款记录表")
@TableName("sys_refund_record")
public class SysRefundRecord extends BaseEntity {

    /**
     * 退款ID（业务标识）
     */
    @Schema(description = "退款ID（业务标识）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String refundId;

    /**
     * 关联支付流水
     */
    @Schema(description = "关联支付流水", requiredMode = Schema.RequiredMode.REQUIRED)
    private String paymentId;

    /**
     * 退款金额
     */
    @Schema(description = "退款金额 美元", example = "100.00", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal amount;

    /**
     * 退款状态
     */
    @Schema(description = "退款状态 0-退款中 1-退款成功 2-退款失败", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;
    /**
     * 渠道退款号
     */
    @Schema(description = "渠道退款号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelRefundNo;
}