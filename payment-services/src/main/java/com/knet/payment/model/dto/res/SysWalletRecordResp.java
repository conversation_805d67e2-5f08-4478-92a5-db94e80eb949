package com.knet.payment.model.dto.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.WalletRecordType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:31
 * @description: 用户钱包记录返回体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysWalletRecordResp extends BaseResponse {
    /**
     * 记录ID（全局唯一）
     */
    @Schema(description = "记录ID（全局唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recordId;
    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;
    /**
     * 变动金额
     */
    @Schema(description = "变动金额 美元", example = "100.00", requiredMode = Schema.RequiredMode.REQUIRED)
    private String amount;
    /**
     * 钱包交易类型
     *
     * @see WalletRecordType
     */
    @Schema(description = "钱包交易类型", example = "PAYMENT_DEDUCTION REFUND_INCOME RECHARGE WITHDRAW DEDUCT", requiredMode = Schema.RequiredMode.REQUIRED)
    private String type;

    /**
     * 钱包记录时间
     */
    @Schema(description = "记录时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;
}
