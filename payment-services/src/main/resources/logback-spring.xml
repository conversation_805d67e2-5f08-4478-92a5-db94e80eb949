<?xml version="1.0" encoding="UTF-8"?>
<!--
    Logback配置文件 - 安全增强版本
    防止Log4Shell等日志注入攻击
    支持错误日志分离和性能优化
-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!-- 安全配置：禁用不安全的功能 -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 应用名称和日志路径配置 -->
    <springProperty scope="context" name="APPLICATION_NAME" source="spring.application.name"/>
    <property name="LOG_FILE_PATH" value="./logs/${APPLICATION_NAME}"/>

    <!-- 日志格式配置 - 增强安全性 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %replace(%msg){'[\r\n\t]', '_'}%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="LOG_PATTERN"
              value="%date{yyyy-MM-dd HH:mm:ss.SSS} [%X{TID}] [%thread] %-5level %logger{36} - %replace(%msg){'[\r\n\t]', '_'}%n"/>
    <!--控制台打印-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!--普通日志输出文件 - 性能优化版本-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_PATH}/info.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天滚动日志文件，单个文件最大100MB -->
            <fileNamePattern>${LOG_FILE_PATH}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个文件最大大小 -->
            <maxFileSize>100MB</maxFileSize>
            <!-- 保留30天的历史记录 -->
            <maxHistory>30</maxHistory>
            <!-- 总日志文件大小限制 -->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <!-- 过滤器，只记录INFO及以上级别的日志，不包括ERROR级别 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <!-- 异步写入提升性能 -->
        <prudent>false</prudent>
    </appender>
    <!--错误日志输出文件 - 独立错误日志-->
    <appender name="error_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_PATH}/error.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 每天滚动日志文件，单个文件最大50MB -->
            <fileNamePattern>${LOG_FILE_PATH}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个文件最大大小 -->
            <maxFileSize>50MB</maxFileSize>
            <!-- 保留60天的历史记录（错误日志保留更久） -->
            <maxHistory>60</maxHistory>
            <!-- 总日志文件大小限制 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <!-- 过滤器，只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 异步写入提升性能 -->
        <prudent>false</prudent>
    </appender>

    <!-- 异步Appender配置 - 提升性能 -->
    <appender name="async_file" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="file"/>
        <queueSize>1024</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>false</includeCallerData>
    </appender>

    <appender name="async_error_file" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="error_file"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
    </appender>

    <!-- 安全配置：防止日志注入攻击 -->
    <logger name="org.apache.logging.log4j" level="ERROR"/>
    <logger name="org.springframework.jndi" level="ERROR"/>

    <!-- 将文件Appender应用到日志的根级别 -->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="async_file"/>
        <appender-ref ref="async_error_file"/>
    </root>
</configuration>
