#!/bin/bash

# 安全启动脚本 - 防止Log4Shell等安全漏洞
# 适用于所有Spring Boot微服务模块

# Log4j2 安全参数 - 防止 Log4Shell (CVE-2021-44228)
SECURITY_OPTS="-Dlog4j2.formatMsgNoLookups=true"
SECURITY_OPTS="$SECURITY_OPTS -Dlog4j2.enableJndiLookup=false"
SECURITY_OPTS="$SECURITY_OPTS -Dlog4j2.enableJndiJms=false"
SECURITY_OPTS="$SECURITY_OPTS -Dlog4j2.enableJndiContextSelector=false"

# 禁用不安全的系统属性
SECURITY_OPTS="$SECURITY_OPTS -Dcom.sun.jndi.rmi.object.trustURLCodebase=false"
SECURITY_OPTS="$SECURITY_OPTS -Dcom.sun.jndi.cosnaming.object.trustURLCodebase=false"

# JVM 安全参数
JVM_OPTS="-server"
JVM_OPTS="$JVM_OPTS -Xms512m -Xmx2g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=./logs/"

# 安全管理器配置（可选）
# JVM_OPTS="$JVM_OPTS -Djava.security.manager"
# JVM_OPTS="$JVM_OPTS -Djava.security.policy=security.policy"

# Spring Boot 配置
SPRING_OPTS="-Dspring.profiles.active=${PROFILE:-dev}"
SPRING_OPTS="$SPRING_OPTS -Dspring.main.allow-circular-references=true"

# 日志配置
LOG_OPTS="-Dlogging.config=classpath:logback-spring.xml"
LOG_OPTS="$LOG_OPTS -Dlog4j.configurationFile=classpath:log4j2-security.properties"

# 组合所有参数
ALL_OPTS="$SECURITY_OPTS $JVM_OPTS $SPRING_OPTS $LOG_OPTS"

echo "=== 安全启动参数 ==="
echo "Security Options: $SECURITY_OPTS"
echo "JVM Options: $JVM_OPTS"
echo "Spring Options: $SPRING_OPTS"
echo "Log Options: $LOG_OPTS"
echo "===================="

# 使用示例：
# export PROFILE=prd
# export JAR_FILE=user-services/target/user-services-1.0-SNAPSHOT.jar
# java $ALL_OPTS -jar $JAR_FILE

echo "使用方法："
echo "export PROFILE=dev|prd"
echo "export JAR_FILE=path/to/your-service.jar"
echo "java $ALL_OPTS -jar \$JAR_FILE"
