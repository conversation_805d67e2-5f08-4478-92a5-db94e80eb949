<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.knet</groupId>
    <artifactId>knet</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>gateway</module>
        <module>user-services</module>
        <module>goods-services</module>
        <module>common</module>
        <module>oauth-services</module>
        <module>order-services</module>
        <module>payment-services</module>
    </modules>

    <!--统一管理jar包版本-->
    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <springCloud.version>2021.0.5</springCloud.version>
        <springBoot.version>2.7.6</springBoot.version>
        <springCloudAlibaba.version>2021.0.5.0</springCloudAlibaba.version>
        <lombok.version>1.18.20</lombok.version>
        <log4j.version>1.2.17</log4j.version>
        <log4j2.version>1.2.17</log4j2.version>
        <mysql.version>8.0.33</mysql.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <hutool.version>5.8.35</hutool.version>
        <fastjson2.version>2.0.34</fastjson2.version>
        <springdoc.version>1.7.0</springdoc.version>
        <mybatisPlus.version>3.5.12</mybatisPlus.version>
        <lettuce.version>6.1.10.RELEASE</lettuce.version>
        <easyexcel.version>4.0.3</easyexcel.version>
        <commonsPool2.version>2.11.1</commonsPool2.version>
        <rocketmqClient.version>2.9.0</rocketmqClient.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springBoot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${springCloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${springCloudAlibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>
            <!--工具-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- 添加 Fastjson2 依赖 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <!-- SpringDoc OpenAPI -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <!-- mybatis plus-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatisPlus.version}</version>
            </dependency>
            <!-- Spring Boot Starter Data Redis -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${springBoot.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commonsPool2.version}</version>
            </dependency>
            <!--easy-excel excel读写工具-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!--参数校验-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-validation</artifactId>
                <version>${springBoot.version}</version>
            </dependency>
            <!-- captcha -->
            <dependency>
                <groupId>com.github.axet</groupId>
                <artifactId>kaptcha</artifactId>
                <version>0.0.9</version>
            </dependency>
            <!-- RocketMQ 5.1.0 核心依赖 排除旧版本-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.rocketmq</groupId>
                        <artifactId>rocketmq-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.rocketmq</groupId>
                        <artifactId>rocketmq-acl</artifactId>
                    </exclusion>
                </exclusions>
                <version>${springCloudAlibaba.version}</version>
            </dependency>
            <!-- RocketMQ 5.1.0 核心依赖 -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>5.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>5.1.0</version>
            </dependency>
            <!-- XXL-JOB 执行器核心依赖 -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.0.RELEASE</version>
                <configuration>
                    <fork>true</fork>
                    <addResources>true</addResources>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>