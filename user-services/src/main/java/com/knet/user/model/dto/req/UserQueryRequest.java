package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/17 09:57
 * @description: 用户查询请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserQueryRequest extends BaseRequest {
    @Schema(description = "页码数")
    private Integer pageNo = 1;

    @Schema(description = "每页条数")
    private Integer pageSize = 10;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String account;

    @Schema(description = "id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long id;
}
